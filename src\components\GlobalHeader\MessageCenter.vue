<template>
  <a-drawer
    title="消息中心"
    placement="right"
    :closable="true"
    :visible="visible"
    :width="400"
    @close="onClose"
    :body-style="{ padding: 0, height: '100%' }"
  >
    <div class="message-container">
      <div class="message-list">
      <div
        v-for="(message, index) in messageList"
        :key="index"
        class="message-item"
      >
        <!-- 预报预警信息 -->
        <div v-if="message.type === 'forecast'" class="message-content">
          <div class="message-title">【{{ message.warningGroup }}-{{ message.warningTarget }}】</div>
          <div class="message-info">
            <div class="info-row">
              <span class="label">预警时间：</span>
              <span>{{ message.warningTime }}</span>
            </div>
            <div class="info-row">
              <span class="label">预警编号：</span>
              <span>{{ message.warningCode }}</span>
            </div>
            <div class="info-row">
              <span class="label">预警消息：</span>
              <span>{{ message.warningMessage }}</span>
            </div>
          </div>
          <div class="message-actions">
            <a-button type="primary" size="small">查看</a-button>
          </div>
        </div>

        <!-- 巡检任务 -->
        <div v-else-if="message.type === 'inspection'" class="message-content">
          <div class="message-title">【{{ message.taskName }}】</div>
          <div class="message-info">
            <div class="info-row">
              <span class="label">计划开始时间：</span>
              <span>{{ message.planStartTime }}</span>
            </div>
            <div class="info-row">
              <span class="label">计划结束时间：</span>
              <span>{{ message.planEndTime }}</span>
            </div>
            <div class="info-row">
              <span class="label">任务编码：</span>
              <span>{{ message.taskCode }}</span>
            </div>
            <div class="info-row">
              <span class="label">巡检范围：</span>
              <span>{{ message.inspectionScope }}</span>
            </div>
          </div>
          <div class="message-actions">
            <a-button type="primary" size="small">查看</a-button>
          </div>
        </div>

        <!-- 应急抢修任务 -->
        <div v-else-if="message.type === 'emergency'" class="message-content">
          <div class="message-title">【应急抢修-{{ message.reportUnit }}】</div>
          <div class="message-info">
            <div class="info-row">
              <span class="label">提报时间：</span>
              <span>{{ message.reportTime }}</span>
            </div>
            <div class="info-row">
              <span class="label">工单编号：</span>
              <span>{{ message.workOrderCode }}</span>
            </div>
            <div class="info-row">
              <span class="label">应急部位：</span>
              <span>{{ message.emergencyLocation }}</span>
            </div>
            <div class="info-row">
              <span class="label">问题描述：</span>
              <span>{{ message.problemDescription }}</span>
            </div>
          </div>
          <div class="message-actions">
            <a-button type="primary" size="small">查看</a-button>
          </div>
        </div>

        <!-- 用水调度 -->
        <div v-else-if="message.type === 'dispatch'" class="message-content">
          <div class="message-title">【用水调度-{{ message.dispatchType }}】</div>
          <div class="message-info">
            <div class="info-row">
              <span class="label">计划调度日期：</span>
              <span>{{ message.planDispatchDate }}</span>
            </div>
            <div class="info-row">
              <span class="label">调度单编号：</span>
              <span>{{ message.dispatchCode }}</span>
            </div>
            <div class="info-row">
              <span class="label">计划调度水量：</span>
              <span>{{ message.planWaterAmount }}</span>
            </div>
          </div>
          <div class="message-actions">
            <a-button type="primary" size="small">查看</a-button>
          </div>
        </div>
      </div>

        <!-- 空状态 -->
        <div v-if="messageList.length === 0" class="empty-state">
          <a-empty description="暂无消息" />
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<script>
export default {
  name: 'MessageCenter',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      messageList: [
        // 预报预警信息
        {
          type: 'forecast',
          warningGroup: '预警分组',
          warningTarget: '预警对象名称',
          warningTime: '2025-08-29 09:52',
          warningCode: '',
          warningMessage: ''
        },
        // 巡检任务
        {
          type: 'inspection',
          taskName: '巡检任务名称',
          planStartTime: '2025-08-29 13:30',
          planEndTime: '2025-08-29 23:00',
          taskCode: 'JHRW-2-2025060601',
          inspectionScope: ''
        },
        // 应急抢修任务
        {
          type: 'emergency',
          reportUnit: '提报单位名称',
          reportTime: '2025-08-29 13:30',
          workOrderCode: 'YJQX20250606162905683',
          emergencyLocation: '雨水情监测站',
          problemDescription: '应急抢修测试'
        },
        // 用水调度
        {
          type: 'dispatch',
          dispatchType: '调度类型名称',
          planDispatchDate: '2025-06-24 11:44:00',
          dispatchCode: 'SLTZ20250623114044509',
          planWaterAmount: '5万m³'
        }
      ]
    }
  },
  methods: {
    onClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="less" scoped>
.message-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  .message-item {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #cbcccc;
    &:last-child {
      margin-bottom: 0;
    }

    .message-content {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .message-title {
        font-weight: 600;
        font-size: 14px;
        color: #262626;
        line-height: 1.4;
      }

      .message-info {
        .info-row {
          display: flex;
          margin-bottom: 8px;
          font-size: 13px;
          line-height: 1.4;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            color: #8c8c8c;
            min-width: 80px;
            flex-shrink: 0;
          }

          span:last-child {
            color: #595959;
            flex: 1;
            word-break: break-all;
          }
        }
      }

      .message-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 8px;
      }
    }
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
}
</style>
